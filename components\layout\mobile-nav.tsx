"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Car, Menu, X, User, Settings, Bell, LogOut, Calendar, Building, Info, Mail, DollarSign, ChevronDown, Globe, Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuRadioGroup, DropdownMenuRadioItem } from "@/components/ui/dropdown-menu"
import { useI18n, languages, type LanguageCode } from "@/i18n/i18n-provider"
import { useCurrency } from "@/contexts/currency-context"
import { cn } from "@/lib/utils"

type CurrencyInfo = {
  symbol: string
  name: string
}

const currencyInfo: Record<string, CurrencyInfo> = {
  MAD: { symbol: "د.م.", name: "Moroccan Dirham" },
  USD: { symbol: "$", name: "US Dollar" },
  EUR: { symbol: "€", name: "Euro" },
  GBP: { symbol: "£", name: "British Pound" },
}

export function MobileNav() {
  const { user, logout, isAgency, isAdmin } = useAuth()
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  const { t, language: currentLanguage, changeLanguage } = useI18n()
  const { currency, setCurrency } = useCurrency()

  const handleLinkClick = (href: string) => {
    router.push(href)
    setIsOpen(false)
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 w-9 p-0 md:hidden"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle mobile menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-center gap-2">
            <Car className="h-6 w-6 text-primary" />
            <span className="ml-2 text-xl font-bold">KriwDrive</span>
          </SheetTitle>
        </SheetHeader>
        <nav className="flex flex-col gap-4 text-sm flex-1 overflow-y-auto py-6">
          {/* Main Navigation Items */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-2">Navigation</h3>
            <Link href="/listings" onClick={() => handleLinkClick("/listings")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
              <Car className="h-5 w-5 text-primary" />
              <span className="font-medium">{t("navbar.browseCars")}</span>
            </Link>
            <Link href="/about" onClick={() => handleLinkClick("/about")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
              <Info className="h-5 w-5 text-primary" />
              <span className="font-medium">{t("navbar.aboutUs")}</span>
            </Link>
            <Link href="/contact" onClick={() => handleLinkClick("/contact")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
              <Mail className="h-5 w-5 text-primary" />
              <span className="font-medium">{t("navbar.contactUs")}</span>
            </Link>
          </div>

          <Separator />

          {/* User Section */}
          {user ? (
            <>
              <div className="space-y-2">
                <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-2">Account</h3>
                <div className="flex items-center gap-3 px-2 py-2">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white font-semibold">
                    {user.firstName ? user.firstName.charAt(0) : "U"}
                  </div>
                  <div>
                    <p className="font-medium">{user.firstName} {user.lastName}</p>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                {isAgency ? (
                  <>
                    <Link href="/agency/dashboard" onClick={() => handleLinkClick("/agency/dashboard")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <Car className="h-5 w-5" />
                      <span>{t("navbar.agencyDashboard")}</span>
                    </Link>
                    <Link href="/agency/dashboard?tab=bookings" onClick={() => handleLinkClick("/agency/dashboard?tab=bookings")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <Calendar className="h-5 w-5" />
                      <span>{t("navbar.bookings")}</span>
                    </Link>
                    <Link href="/agency/dashboard?tab=settings" onClick={() => handleLinkClick("/agency/dashboard?tab=settings")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <Settings className="h-5 w-5" />
                      <span>{t("navbar.settings")}</span>
                    </Link>
                  </>
                ) : (
                  <>
                    <Link href="/user/dashboard" onClick={() => handleLinkClick("/user/dashboard")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <User className="h-5 w-5" />
                      <span>{t("navbar.profile")}</span>
                    </Link>
                    <Link href="/user/dashboard?tab=settings" onClick={() => handleLinkClick("/user/dashboard?tab=settings")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <Settings className="h-5 w-5" />
                      <span>{t("navbar.settings")}</span>
                    </Link>
                    <Link href="/user/dashboard?tab=notifications" onClick={() => handleLinkClick("/user/dashboard?tab=notifications")} className="flex items-center gap-3 px-2 py-3 rounded-md hover:bg-muted transition-colors">
                      <Bell className="h-5 w-5" />
                      <span>{t("navbar.notifications")}</span>
                    </Link>
                  </>
                )}

                <Button
                  variant="ghost"
                  className="w-full justify-start text-red-500 hover:text-red-500 hover:bg-red-50"
                  onClick={() => { logout(); setIsOpen(false); router.push("/"); }}
                >
                  <LogOut className="mr-3 h-5 w-5" />
                  <span>{t("navbar.logout")}</span>
                </Button>
              </div>
            </>
          ) : (
            <div className="space-y-2">
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-2">Authentication</h3>
              <div className="flex flex-col gap-2 px-2">
                <Button onClick={() => handleLinkClick("/auth")} className="w-full">
                  {t("navbar.logIn")}
                </Button>
                <Button variant="outline" onClick={() => handleLinkClick("/auth?tab=signup")} className="w-full">
                  {t("navbar.signUp")}
                </Button>
              </div>
            </div>
          )}

          <Separator />

          {/* Settings Section */}
          <div className="space-y-2">
            <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-2">Settings</h3>

            {/* Currency Selector */}
            <div className="px-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between h-10"
                  >
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      <span>{currency}</span>
                    </div>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuRadioGroup
                    value={currency}
                    onValueChange={(value) => setCurrency(value as "MAD" | "USD" | "EUR" | "GBP")}
                  >
                    {Object.entries(currencyInfo).map(([code, info]) => (
                      <DropdownMenuRadioItem key={code} value={code} className="cursor-pointer">
                        <div className="flex items-center justify-between w-full">
                          <span>
                            {info.symbol} {code}
                          </span>
                          <span className="text-xs text-muted-foreground">{info.name}</span>
                        </div>
                      </DropdownMenuRadioItem>
                    ))}
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Language Selector */}
            <div className="px-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-between h-10"
                  >
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <span>{languages[currentLanguage as LanguageCode].flag}</span>
                    </div>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px]">
                  {(Object.keys(languages) as LanguageCode[]).map((langCode) => (
                    <DropdownMenuItem
                      key={langCode}
                      className={cn(
                        "cursor-pointer flex items-center justify-between",
                        currentLanguage === langCode && "font-medium",
                      )}
                      onClick={() => changeLanguage(langCode)}
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-base">{languages[langCode].flag}</span>
                        <span>{languages[langCode].name}</span>
                      </div>
                      {currentLanguage === langCode && <Check className="h-4 w-4" />}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </nav>
      </SheetContent>
    </Sheet>
  )
}
