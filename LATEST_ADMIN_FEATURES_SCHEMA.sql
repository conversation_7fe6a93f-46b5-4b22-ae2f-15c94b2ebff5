-- =============================================
-- LATEST ADMIN FEATURES - SQL SCHEMA
-- Execute this if you already have the base schema
-- =============================================

-- =============================================
-- BLOGS TABLE
-- =============================================
CREATE TABLE public.blogs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image TEXT,
    slug TEXT UNIQUE NOT NULL,
    status TEXT CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    author_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    tags TEXT[] DEFAULT '{}',
    meta_title TEXT,
    meta_description TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- MESSAGES TABLE (Contact Us & Payment Messages)
-- =============================================
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT CHECK (type IN ('contact', 'payment', 'support')) DEFAULT 'contact',
    status TEXT CHECK (status IN ('unread', 'read', 'replied', 'archived')) DEFAULT 'unread',
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    priority TEXT CHECK (priority IN ('low', 'medium', 'high', 'urgent')) DEFAULT 'medium',
    admin_notes TEXT,
    replied_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AGENCY DOCUMENTS TABLE
-- =============================================
CREATE TABLE public.agency_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    agency_id UUID REFERENCES public.agencies(id) ON DELETE CASCADE NOT NULL,
    document_type TEXT CHECK (document_type IN ('license', 'insurance', 'registration', 'verification', 'other')) NOT NULL,
    document_name TEXT NOT NULL,
    document_url TEXT NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE, -- For documents that should appear on reservation page
    verified_by UUID REFERENCES public.users(id) ON DELETE SET NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR NEW TABLES
-- =============================================

-- Blogs indexes
CREATE INDEX idx_blogs_status ON public.blogs(status);
CREATE INDEX idx_blogs_published_at ON public.blogs(published_at);
CREATE INDEX idx_blogs_slug ON public.blogs(slug);
CREATE INDEX idx_blogs_author_id ON public.blogs(author_id);

-- Messages indexes
CREATE INDEX idx_messages_status ON public.messages(status);
CREATE INDEX idx_messages_type ON public.messages(type);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);

-- Agency documents indexes
CREATE INDEX idx_agency_documents_agency_id ON public.agency_documents(agency_id);
CREATE INDEX idx_agency_documents_type ON public.agency_documents(document_type);
CREATE INDEX idx_agency_documents_verified ON public.agency_documents(is_verified);
CREATE INDEX idx_agency_documents_public ON public.agency_documents(is_public);

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================
ALTER TABLE public.blogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_documents ENABLE ROW LEVEL SECURITY;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- BLOGS POLICIES
CREATE POLICY "Published blogs are viewable by all" ON public.blogs
    FOR SELECT USING (status = 'published');

CREATE POLICY "Admins can manage all blogs" ON public.blogs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- MESSAGES POLICIES
CREATE POLICY "Anyone can create messages" ON public.messages
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can manage all messages" ON public.messages
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- AGENCY DOCUMENTS POLICIES
CREATE POLICY "Public documents are viewable by all" ON public.agency_documents
    FOR SELECT USING (is_public = true);

CREATE POLICY "Agency owners can view their documents" ON public.agency_documents
    FOR SELECT USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Agency owners can manage their documents" ON public.agency_documents
    FOR ALL USING (
        agency_id IN (
            SELECT id FROM public.agencies WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all agency documents" ON public.agency_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Update updated_at column function (if not exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_blogs_updated_at BEFORE UPDATE ON public.blogs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agency_documents_updated_at BEFORE UPDATE ON public.agency_documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- STORAGE BUCKET FOR BLOG IMAGES (if not exists)
-- =============================================
INSERT INTO storage.buckets (id, name, public) 
SELECT 'blog-images', 'blog-images', true
WHERE NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'blog-images');

-- Storage policies for blog images
CREATE POLICY "Blog images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'blog-images');

CREATE POLICY "Admins can upload blog images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'blog-images' AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can delete blog images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'blog-images' AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- =============================================
-- SAMPLE DATA (Optional)
-- =============================================

-- Sample blog post (uncomment if you want a test blog)
-- INSERT INTO public.blogs (title, content, excerpt, slug, status, author_id, published_at)
-- VALUES (
--     'Welcome to MoDrivet Blog',
--     'This is our first blog post. We''re excited to share news, tips, and updates about car rentals.',
--     'Welcome to our new blog where we share car rental tips and updates.',
--     'welcome-to-modrivet-blog',
--     'published',
--     (SELECT id FROM public.users WHERE role = 'admin' LIMIT 1),
--     NOW()
-- );

-- =============================================
-- VERIFICATION COMPLETE
-- =============================================

-- Verify tables were created
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('blogs', 'messages', 'agency_documents')
ORDER BY table_name;

-- Verify RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('blogs', 'messages', 'agency_documents')
ORDER BY tablename, policyname;
