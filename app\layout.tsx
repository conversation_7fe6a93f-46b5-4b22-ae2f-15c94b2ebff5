import type React from "react"
import type { Metada<PERSON> } from "next"
import { Mulish } from "next/font/google"
import "./globals.css"
import "./rtl.css"
import { Toaster } from "sonner"
import { AuthProvider } from "@/contexts/auth-context"
import { MessagesProvider } from "@/contexts/messages-context"
import { MainNavbar } from "@/components/layout/main-navbar"
import { SiteFooter } from "@/components/layout/site-footer"
import { I18nProvider } from "@/i18n/i18n-provider"
import { CurrencyProvider } from "@/contexts/currency-context"
import ClientLayoutWrapper from "@/components/common/client-layout-wrapper"
import { PaymentRequestsProvider } from "@/contexts/payment-requests-context"
import { BookingsProvider } from "@/contexts/bookings-context"

const mulish = Mulish({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-mulish",
})

export const metadata: Metadata = {
  title: "KriwDrive - Car Rental in Morocco",
  description: "Discover Morocco on wheels with KriwDrive car rental service",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={mulish.variable}>
        <I18nProvider>
          <AuthProvider>
            <CurrencyProvider>
              <MessagesProvider>
                <PaymentRequestsProvider>
                  <BookingsProvider>
                    <ClientLayoutWrapper>
                      <MainNavbar />
                      {children}
                      <SiteFooter />
                    </ClientLayoutWrapper>
                  </BookingsProvider>
                </PaymentRequestsProvider>
              </MessagesProvider>
            </CurrencyProvider>
          </AuthProvider>
        </I18nProvider>
        <Toaster />
      </body>
    </html>
  )
}
