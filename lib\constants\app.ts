// Re-export from centralized configuration
export { APP_CONFIG } from '@/config/app.config';
export { ROUTES, API_ENDPOINTS, CAR_BRANDS, CAR_COLORS, CAR_CATEGORIES, USER_ROLES, BOOKING_STATUS, CAR_STATUS, AGENCY_STATUS } from '@/utils/constants';

export const API_ENDPOINTS = {
    auth: {
        login: '/api/auth/login',
        register: '/api/auth/register',
        logout: '/api/auth/logout',
        verify: '/api/auth/verify',
    },
    users: {
        profile: '/api/users/profile',
        update: '/api/users/update',
    },
    agencies: {
        list: '/api/agencies',
        details: '/api/agencies/:id',
        create: '/api/agencies',
        update: '/api/agencies/:id',
    },
    cars: {
        list: '/api/cars',
        details: '/api/cars/:id',
        create: '/api/cars',
        update: '/api/cars/:id',
        delete: '/api/cars/:id',
    },
    bookings: {
        list: '/api/bookings',
        create: '/api/bookings',
        update: '/api/bookings/:id',
        cancel: '/api/bookings/:id/cancel',
    },
    gps: {
        location: '/api/gps/location',
        track: '/api/gps/track',
    },
} as const;

export const USER_ROLES = {
    USER: 'user',
    AGENCY: 'agency',
    ADMIN: 'admin',
} as const;

export const BOOKING_STATUS = {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    ACTIVE: 'active',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled',
} as const;

export const CAR_STATUS = {
    AVAILABLE: 'available',
    RENTED: 'rented',
    MAINTENANCE: 'maintenance',
    UNAVAILABLE: 'unavailable',
} as const; 